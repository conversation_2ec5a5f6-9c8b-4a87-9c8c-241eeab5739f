<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Loan Application Form</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .form-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            background-image: url('application_header.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            height: 120px;
            position: relative;
            overflow: hidden;
        }
        
        .section {
            margin: 0;
        }
        
        .section-header {
            background: #2c3e50;
            color: white;
            padding: 12px 20px;
            font-weight: bold;
            font-size: 14px;
            letter-spacing: 2px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .section-number {
            background: #d2691e;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }
        
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="radio"] {
            width: 16px;
            height: 16px;
        }

        .checkbox-item label {
            margin: 0;
            font-weight: normal;
            font-size: 13px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        
        input[type="text"], input[type="email"], input[type="tel"], select, textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        input[type="text"]:focus, input[type="email"]:focus, input[type="tel"]:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #d2691e;
        }

        /* Required field styling */
        .required-field {
            border-color: #dc3545 !important;
            background-color: #fff5f5;
        }

        .required-label::after {
            content: " *";
            color: #dc3545;
            font-weight: bold;
        }

        .validation-error {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .validation-error.show {
            display: block;
        }

        .form-group {
            position: relative;
        }
        
        .date-group {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .date-input-modern {
            width: 150px;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background-color: #f8f8f8;
        }

        .date-input-modern:focus {
            outline: none;
            border-color: #d2691e;
        }

        .date-label {
            font-size: 12px;
            color: #666;
            margin: 0 5px;
            font-weight: bold;
        }

        .date-separator {
            font-size: 14px;
            color: #666;
            margin: 0 5px;
        }
        
        .others-input {
            margin-top: 10px;
        }
        
        .page-indicator {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            text-align: right;
            font-size: 14px;
            font-weight: bold;
        }
        
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
        }

        .nav-btn {
            background: #d2691e;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .nav-btn:hover {
            background: #b8551a;
        }

        .nav-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .nav-btn.disabled {
            background: #95a5a6;
            cursor: not-allowed;
            pointer-events: none;
        }

        .validation-summary {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 20px;
            display: none;
        }

        .validation-summary.show {
            display: block;
        }

        .validation-summary ul {
            margin: 10px 0 0 20px;
        }

        .continue-btn {
            background: #d2691e;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
            margin: 20px;
        }

        .continue-btn:hover {
            background: #b8551a;
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .form-container {
                max-width: 100%;
                margin: 0;
                box-shadow: none;
            }

            .header {
                padding: 15px;
                height: 100px;
            }

            .section-content {
                padding: 15px;
            }

            .form-row {
                flex-direction: column;
                gap: 10px;
            }

            .checkbox-group {
                flex-direction: column;
                gap: 10px;
            }

            .checkbox-item {
                margin-bottom: 5px;
            }

            .navigation-buttons {
                flex-direction: column;
                gap: 15px;
                padding: 15px;
            }

            .nav-btn {
                width: 100%;
                text-align: center;
            }

            .date-group {
                flex-wrap: wrap;
                gap: 10px;
            }

            .date-input-modern {
                width: 100%;
                max-width: 200px;
            }

            .others-input {
                margin-top: 5px;
            }
        }

        @media (max-width: 480px) {
            .header {
                height: 80px;
                padding: 10px;
            }

            .section-content {
                padding: 10px;
            }

            .section-header {
                padding: 10px 15px;
                font-size: 12px;
            }

            .section-number {
                width: 20px;
                height: 20px;
                font-size: 12px;
            }

            .form-group label {
                font-size: 13px;
            }

            input[type="text"], input[type="email"], input[type="tel"], select, textarea {
                padding: 8px;
                font-size: 13px;
            }

            .checkbox-item label {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="form-container">
        <div class="header">
            <!-- Header content is now part of the background image -->
        </div>
        
        <div class="section">
            <div class="section-header">
                <div class="section-number">1</div>
                <span>REASON FOR LOAN</span>
            </div>
            <div class="section-content">
                <div class="form-group">
                    <label>Purpose of loan</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="radio" id="invoice" name="loan_purpose">
                            <label for="invoice">Invoice Discounting</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="payroll" name="loan_purpose">
                            <label for="payroll">Payroll Funding</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="expansion" name="loan_purpose">
                            <label for="expansion">Expansion Finance</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="bridging" name="loan_purpose">
                            <label for="bridging">Bridging Finance</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="purchase" name="loan_purpose">
                            <label for="purchase">Purchase Order Finance</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="equipment" name="loan_purpose">
                            <label for="equipment">Equipment Acquisition Finance</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="project" name="loan_purpose">
                            <label for="project">Project Finance</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="others" name="loan_purpose">
                            <label for="others">Others, specify:</label>
                        </div>
                    </div>
                    <input type="text" class="others-input" placeholder="">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>Loan amount</label>
                        <input type="text" placeholder="">
                    </div>
                    <div class="form-group">
                        <label>Loan period (not exceeding 60 days)</label>
                        <input type="text" placeholder="">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">
                <div class="section-number">2</div>
                <span>BUSINESS DETAILS</span>
            </div>
            <div class="section-content">
                <div class="form-group">
                    <label>Business name</label>
                    <input type="text" placeholder="">
                </div>
                
                <div class="form-group">
                    <label>Entity type</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="radio" id="close-corp" name="entity_type">
                            <label for="close-corp">Close Corporation</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="private-company" name="entity_type">
                            <label for="private-company">Private Company</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="trust" name="entity_type">
                            <label for="trust">Trust</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="joint-venture" name="entity_type">
                            <label for="joint-venture">Joint Venture</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Date of incorporation</label>
                    <div class="date-group">
                        <input type="date" class="date-input-modern" id="incorporation_date" name="incorporation_date">
                        <span class="date-label">(DD/MM/YYYY)</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Registration number</label>
                    <input type="text" placeholder="">
                </div>

                <div class="form-group">
                    <label>Business sector</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="radio" id="manufacturing" name="business_sector">
                            <label for="manufacturing">Manufacturing</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="retail" name="business_sector">
                            <label for="retail">Retail</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="services" name="business_sector">
                            <label for="services">Services</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="consulting" name="business_sector">
                            <label for="consulting">Consulting</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="hospitality" name="business_sector">
                            <label for="hospitality">Hospitality</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="logistics" name="business_sector">
                            <label for="logistics">Logistics</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="others-sector" name="business_sector">
                            <label for="others-sector">Others, specify:</label>
                        </div>
                    </div>
                    <input type="text" class="others-input" placeholder="">
                </div>

                <div class="form-group">
                    <label>Office address</label>
                    <input type="text" placeholder="">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Province</label>
                        <input type="text" placeholder="">
                    </div>
                    <div class="form-group">
                        <label>City</label>
                        <input type="text" placeholder="">
                    </div>
                    <div class="form-group">
                        <label>Postcode</label>
                        <input type="text" placeholder="">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Landline</label>
                        <input type="tel" placeholder="">
                    </div>
                    <div class="form-group">
                        <label>Mobile</label>
                        <input type="tel" placeholder="">
                    </div>
                </div>

                <div class="form-group">
                    <label>Email address</label>
                    <input type="email" placeholder="">
                </div>
            </div>
        </div>

        <div class="navigation-buttons">
            <span></span> <!-- Empty space for alignment -->
            <a href="business-loan-form-page2.html" class="nav-btn">Next →</a>
        </div>

        <div class="page-indicator">
            1/3
        </div>
    </div>

    <script>
    // Save form data when user makes changes
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('.form-container');

        // Load existing data
        loadFormData();

        // Save data on input changes
        form.addEventListener('change', saveFormData);
        form.addEventListener('input', saveFormData);

        // Add validation summary to the page
        const validationSummary = document.createElement('div');
        validationSummary.className = 'validation-summary';
        validationSummary.innerHTML = '<h4>Please correct the following errors:</h4><ul></ul>';
        form.insertBefore(validationSummary, form.querySelector('.navigation-buttons'));

        // Mark required fields
        markRequiredFields();

        // Add validation to the Next button
        setupNextButtonValidation();
    });

    function saveFormData() {
        const existingData = JSON.parse(localStorage.getItem('qoreFormData') || '{}');

        // Get all input fields more reliably
        const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"]');
        const dateInput = document.querySelector('#incorporation_date');

        const formData = {
            ...existingData,
            // Page 1 data
            loan_purpose: document.querySelector('input[name="loan_purpose"]:checked')?.nextElementSibling?.textContent?.trim() || '',
            others_specify: document.querySelector('.others-input')?.value || '',
            loan_amount: inputs[0]?.value || '',
            loan_period: inputs[1]?.value || '',
            business_name: inputs[2]?.value || '',
            entity_type: document.querySelector('input[name="entity_type"]:checked')?.nextElementSibling?.textContent?.trim() || '',
            incorporation_date: dateInput?.value || '',
            registration_number: inputs[3]?.value || '',
            business_sector: document.querySelector('input[name="business_sector"]:checked')?.nextElementSibling?.textContent?.trim() || '',
            office_address: inputs[5]?.value || '',
            province: inputs[6]?.value || '',
            city: inputs[7]?.value || '',
            postcode: inputs[8]?.value || '',
            landline: inputs[9]?.value || '',
            mobile: inputs[10]?.value || '',
            business_email: inputs[11]?.value || ''
        };

        localStorage.setItem('qoreFormData', JSON.stringify(formData));
    }

    // Validation functions
    function markRequiredFields() {
        // Define required fields for Page 1
        const requiredFields = [
            'input[name="loan_purpose"]', // Radio buttons for loan purpose
            'input[placeholder=""][data-field="loan_amount"]',
            'input[placeholder=""][data-field="loan_period"]',
            'input[placeholder=""][data-field="business_name"]',
            'input[placeholder=""][data-field="registration_number"]',
            'input[placeholder=""][data-field="office_address"]',
            'input[placeholder=""][data-field="province"]',
            'input[placeholder=""][data-field="city"]',
            'input[placeholder=""][data-field="mobile"]',
            'input[type="email"]'
        ];

        // Add data attributes to inputs for easier identification
        const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"]');
        const fieldNames = ['loan_amount', 'loan_period', 'business_name', 'registration_number', 'office_address', 'province', 'city', 'postcode', 'landline', 'mobile', 'business_email'];

        inputs.forEach((input, index) => {
            if (fieldNames[index]) {
                input.setAttribute('data-field', fieldNames[index]);
            }
        });

        // Mark labels as required
        const requiredLabels = [
            'Loan amount', 'Loan period', 'Business name', 'Registration number',
            'Office address', 'Province', 'City', 'Mobile', 'Email address'
        ];

        document.querySelectorAll('label').forEach(label => {
            if (requiredLabels.some(req => label.textContent.includes(req))) {
                label.classList.add('required-label');
            }
        });
    }

    function validatePage1() {
        const errors = [];

        // Check loan purpose selection
        const loanPurpose = document.querySelector('input[name="loan_purpose"]:checked');
        if (!loanPurpose) {
            errors.push('Please select a reason for the loan');
        }

        // Check required text fields
        const requiredFields = [
            { selector: 'input[data-field="loan_amount"]', name: 'Loan amount' },
            { selector: 'input[data-field="loan_period"]', name: 'Loan period' },
            { selector: 'input[data-field="business_name"]', name: 'Business name' },
            { selector: 'input[data-field="registration_number"]', name: 'Registration number' },
            { selector: 'input[data-field="office_address"]', name: 'Office address' },
            { selector: 'input[data-field="province"]', name: 'Province' },
            { selector: 'input[data-field="city"]', name: 'City' },
            { selector: 'input[data-field="mobile"]', name: 'Mobile number' },
            { selector: 'input[type="email"]', name: 'Email address' }
        ];

        requiredFields.forEach(field => {
            const element = document.querySelector(field.selector);
            if (element && (!element.value || element.value.trim() === '')) {
                errors.push(`${field.name} is required`);
                element.classList.add('required-field');
            } else if (element) {
                element.classList.remove('required-field');
            }
        });

        // Validate loan amount is numeric and positive
        const loanAmount = document.querySelector('input[data-field="loan_amount"]');
        if (loanAmount && loanAmount.value) {
            const amount = parseFloat(loanAmount.value.replace(/[^\d.]/g, ''));
            if (isNaN(amount) || amount <= 0) {
                errors.push('Loan amount must be a valid positive number');
                loanAmount.classList.add('required-field');
            }
        }

        // Validate loan period is numeric and within range
        const loanPeriod = document.querySelector('input[data-field="loan_period"]');
        if (loanPeriod && loanPeriod.value) {
            const period = parseInt(loanPeriod.value);
            if (isNaN(period) || period <= 0 || period > 60) {
                errors.push('Loan period must be between 1 and 60 days');
                loanPeriod.classList.add('required-field');
            }
        }

        return errors;
    }

    function setupNextButtonValidation() {
        const nextButton = document.querySelector('a[href="business-loan-form-page2.html"]');
        if (nextButton) {
            nextButton.addEventListener('click', function(e) {
                e.preventDefault();

                const errors = validatePage1();
                if (errors.length > 0) {
                    displayValidationErrors(errors);
                    return false;
                } else {
                    hideValidationErrors();
                    // Save data before navigation
                    saveFormData();
                    // Navigate to next page
                    window.location.href = 'business-loan-form-page2.html';
                }
            });
        }
    }

    function displayValidationErrors(errors) {
        const validationSummary = document.querySelector('.validation-summary');
        const errorList = validationSummary.querySelector('ul');

        // Clear previous errors
        errorList.innerHTML = '';

        // Add new errors
        errors.forEach(error => {
            const li = document.createElement('li');
            li.textContent = error;
            errorList.appendChild(li);
        });

        // Show validation summary
        validationSummary.classList.add('show');

        // Scroll to validation summary
        validationSummary.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    function hideValidationErrors() {
        const validationSummary = document.querySelector('.validation-summary');
        validationSummary.classList.remove('show');

        // Remove required-field styling from all inputs
        document.querySelectorAll('.required-field').forEach(field => {
            field.classList.remove('required-field');
        });
    }

    // Add validation for phone numbers
    function validatePhoneNumber(input) {
        const value = input.value.replace(/\D/g, ''); // Remove non-digits
        if (value.length > 0 && value.length !== 10) {
            input.setCustomValidity('Mobile number must be exactly 10 digits');
            input.style.borderColor = '#dc3545';
        } else {
            input.setCustomValidity('');
            input.style.borderColor = '#ddd';
        }
    }

    // Add event listeners for validation
    document.addEventListener('DOMContentLoaded', function() {
        // Find mobile number inputs and add validation
        const mobileInputs = document.querySelectorAll('input[type="tel"]');
        mobileInputs.forEach(input => {
            input.addEventListener('input', function() {
                // Only allow digits
                this.value = this.value.replace(/\D/g, '');
                validatePhoneNumber(this);
            });

            input.addEventListener('blur', function() {
                validatePhoneNumber(this);
            });
        });
    });

    function loadFormData() {
        const savedData = JSON.parse(localStorage.getItem('qoreFormData') || '{}');

        // Load saved data back into form fields
        if (savedData.loan_purpose) {
            const purposeInputs = document.querySelectorAll('input[name="loan_purpose"]');
            purposeInputs.forEach(input => {
                if (input.nextElementSibling.textContent === savedData.loan_purpose) {
                    input.checked = true;
                }
            });
        }

        // Load other fields...
        const inputs = document.querySelectorAll('input[placeholder=""]');
        const values = [
            savedData.loan_amount, savedData.loan_period, savedData.business_name,
            savedData.registration_number, savedData.office_address, savedData.province,
            savedData.city, savedData.postcode, savedData.landline, savedData.mobile,
            savedData.business_email
        ];

        inputs.forEach((input, index) => {
            if (values[index]) {
                input.value = values[index];
            }
        });
    }
    </script>
</body>
</html>
